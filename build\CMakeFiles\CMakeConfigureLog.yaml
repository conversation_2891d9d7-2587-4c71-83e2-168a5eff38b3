
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 8/11/2025 1:16:05 AM.
      
      Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.53
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/Frank Work All/CPP/f0/build/CMakeFiles/4.1.0/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.com"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Users/<USER>/scoop/shims/mt.com"
      - "C:/Users/<USER>/scoop/shims/mt.exe"
      - "C:/Users/<USER>/scoop/shims/mt"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/.dotnet/tools/mt.com"
      - "C:/Users/<USER>/.dotnet/tools/mt.exe"
      - "C:/Users/<USER>/.dotnet/tools/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/ProgramData/mingw64/mingw64/bin/mt.com"
      - "C:/ProgramData/mingw64/mingw64/bin/mt.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 8/11/2025 1:16:05 AM.
      
      Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.55
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Frank Work All/CPP/f0/build/CMakeFiles/4.1.0/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.com"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:574 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:547 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "C:/Program Files (x86)/MySDL3Game/sbin/"
      - "C:/Program Files (x86)/MySDL3Game/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Users/<USER>/scoop/shims/rc.com"
      - "C:/Users/<USER>/scoop/shims/rc.exe"
      - "C:/Users/<USER>/scoop/shims/rc"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/.dotnet/tools/rc.com"
      - "C:/Users/<USER>/.dotnet/tools/rc.exe"
      - "C:/Users/<USER>/.dotnet/tools/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/ProgramData/mingw64/mingw64/bin/rc.com"
      - "C:/ProgramData/mingw64/mingw64/bin/rc.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc"
      - "C:/Program Files (x86)/MySDL3Game/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qt9w4k"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qt9w4k"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qt9w4k'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_fee5a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 1:16:06 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qt9w4k\\cmTC_fee5a.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_fee5a.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qt9w4k\\Debug\\".
          Creating directory "cmTC_fee5a.dir\\Debug\\cmTC_fee5a.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_fee5a.dir\\Debug\\cmTC_fee5a.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_fee5a.dir\\Debug\\cmTC_fee5a.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_fee5a.dir\\Debug\\\\" /Fd"cmTC_fee5a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_fee5a.dir\\Debug\\\\" /Fd"cmTC_fee5a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qt9w4k\\Debug\\cmTC_fee5a.exe" /INCREMENTAL /ILK:"cmTC_fee5a.dir\\Debug\\cmTC_fee5a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qt9w4k/Debug/cmTC_fee5a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qt9w4k/Debug/cmTC_fee5a.lib" /MACHINE:X64  /machine:x64 cmTC_fee5a.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_fee5a.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qt9w4k\\Debug\\cmTC_fee5a.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_fee5a.dir\\Debug\\cmTC_fee5a.tlog\\unsuccessfulbuild".
          Touching "cmTC_fee5a.dir\\Debug\\cmTC_fee5a.tlog\\cmTC_fee5a.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qt9w4k\\cmTC_fee5a.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34809.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-2y7t43"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-2y7t43"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-2y7t43'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_af33f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 1:16:07 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2y7t43\\cmTC_af33f.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_af33f.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2y7t43\\Debug\\".
          Creating directory "cmTC_af33f.dir\\Debug\\cmTC_af33f.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_af33f.dir\\Debug\\cmTC_af33f.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_af33f.dir\\Debug\\cmTC_af33f.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_af33f.dir\\Debug\\\\" /Fd"cmTC_af33f.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_af33f.dir\\Debug\\\\" /Fd"cmTC_af33f.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2y7t43\\Debug\\cmTC_af33f.exe" /INCREMENTAL /ILK:"cmTC_af33f.dir\\Debug\\cmTC_af33f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-2y7t43/Debug/cmTC_af33f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-2y7t43/Debug/cmTC_af33f.lib" /MACHINE:X64  /machine:x64 cmTC_af33f.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_af33f.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2y7t43\\Debug\\cmTC_af33f.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_af33f.dir\\Debug\\cmTC_af33f.tlog\\unsuccessfulbuild".
          Touching "cmTC_af33f.dir\\Debug\\cmTC_af33f.tlog\\cmTC_af33f.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2y7t43\\cmTC_af33f.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.56
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34809.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

#pragma once

#include <SDL3/SDL.h>
#include <string>
#include <vector>
#include "Position.h"

enum class MenuAction {
    NONE,
    INTERACT,
    MOVE_TO_POSITION
};

struct MenuOption {
    std::string text;
    MenuAction action;
    SDL_FRect rect;
    
    MenuOption(const std::string& txt, MenuAction act) : text(txt), action(act) {}
};

class SelectionMenu {
private:
    std::vector<MenuOption> options;
    Position clickPosition;
    bool isVisible;
    SDL_FRect menuRect;
    int selectedOption;
    
    void calculateMenuRect(int mouseX, int mouseY);
    
public:
    SelectionMenu();
    
    // Menu management
    void show(int mouseX, int mouseY, const Position& clickPos, const std::string& entityName);
    void hide();
    bool getIsVisible() const { return isVisible; }
    
    // Input handling
    MenuAction handleClick(int mouseX, int mouseY);
    void handleMouseMove(int mouseX, int mouseY);
    
    // Rendering
    void render(SDL_Renderer* renderer);
    
    // Getters
    const Position& getClickPosition() const { return clickPosition; }
};

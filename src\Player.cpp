#include "Player.h"
#include "TileMap.h"
#include "Entity.h"
#include <queue>
#include <cmath>
#include <iostream>

Player::Player(int startX, int startY)
    : currentPos(startX, startY),
      targetPos(startX, startY),
      pathIndex(0),
      moveSpeed(100.0f),
      hasTarget(false)
{
    pixelX = startX * TILE_SIZE + TILE_SIZE / 2;
    pixelY = startY * TILE_SIZE + TILE_SIZE / 2;
}

void Player::setTarget(const Position &target, const TileMap &map)
{
    if (map.isWalkable(target.x, target.y))
    {
        targetPos = target;
        path = findPath(currentPos, targetPos, map);
        pathIndex = 0;
        hasTarget = true;
    }
}

void Player::update(float deltaTime)
{
    if (!path.empty() && pathIndex < path.size())
    {
        Position nextTile = path[pathIndex];
        float targetPixelX = nextTile.x * TILE_SIZE + TILE_SIZE / 2;
        float targetPixelY = nextTile.y * TILE_SIZE + TILE_SIZE / 2;

        float dx = targetPixelX - pixelX;
        float dy = targetPixelY - pixelY;
        float distance = sqrt(dx * dx + dy * dy);

        if (distance < 2.0f)
        {
            // Đã đến tile tiếp theo
            pixelX = targetPixelX;
            pixelY = targetPixelY;
            currentPos = nextTile;
            pathIndex++;

            // Kiểm tra nếu đã đến đích
            if (pathIndex >= path.size())
            {
                hasTarget = false;
            }
        }
        else
        {
            // Di chuyển về phía tile tiếp theo
            float moveDistance = moveSpeed * deltaTime;
            pixelX += (dx / distance) * moveDistance;
            pixelY += (dy / distance) * moveDistance;
        }
    }
}

void Player::render(SDL_Renderer *renderer)
{
    // Vẽ target nếu có
    if (hasTarget)
    {
        SDL_FRect targetRect = {
            static_cast<float>(targetPos.x * TILE_SIZE + 4),
            static_cast<float>(targetPos.y * TILE_SIZE + 4),
            static_cast<float>(TILE_SIZE - 8),
            static_cast<float>(TILE_SIZE - 8)};
        SDL_SetRenderDrawColor(renderer, 0, 0, 255, 100); // Màu xanh dương cho target
        SDL_RenderFillRect(renderer, &targetRect);
    }

    // Vẽ đường đi
    SDL_SetRenderDrawColor(renderer, 0, 255, 0, 128); // Màu xanh lá cho đường đi
    for (size_t i = pathIndex; i < path.size(); i++)
    {
        SDL_FRect pathRect = {
            static_cast<float>(path[i].x * TILE_SIZE + 8),
            static_cast<float>(path[i].y * TILE_SIZE + 8),
            static_cast<float>(TILE_SIZE - 16),
            static_cast<float>(TILE_SIZE - 16)};
        SDL_RenderFillRect(renderer, &pathRect);
    }

    // Vẽ nhân vật
    SDL_FRect rect = {
        pixelX - 8,
        pixelY - 8,
        16,
        16};

    SDL_SetRenderDrawColor(renderer, 255, 0, 0, 255); // Màu đỏ cho nhân vật
    SDL_RenderFillRect(renderer, &rect);

    // Vẽ viền cho nhân vật
    SDL_SetRenderDrawColor(renderer, 150, 0, 0, 255);
    SDL_RenderRect(renderer, &rect);
}

std::vector<Position> Player::findPath(const Position &start, const Position &end, const TileMap &map)
{
    std::vector<Position> result;

    // Pathfinding đơn giản sử dụng BFS
    std::queue<std::vector<Position>> queue;
    std::vector<std::vector<bool>> visited(MAP_HEIGHT, std::vector<bool>(MAP_WIDTH, false));

    queue.push({start});
    visited[start.y][start.x] = true;

    int dx[] = {0, 1, 0, -1};
    int dy[] = {-1, 0, 1, 0};

    while (!queue.empty())
    {
        std::vector<Position> currentPath = queue.front();
        queue.pop();

        Position current = currentPath.back();

        if (current == end)
        {
            result = currentPath;
            break;
        }

        for (int i = 0; i < 4; i++)
        {
            int newX = current.x + dx[i];
            int newY = current.y + dy[i];

            if (map.isWalkable(newX, newY) && !visited[newY][newX])
            {
                visited[newY][newX] = true;
                std::vector<Position> newPath = currentPath;
                newPath.push_back(Position(newX, newY));
                queue.push(newPath);
            }
        }
    }

    return result;
}

void Player::interactWith(Entity *entity)
{
    if (entity)
    {
        std::cout << "Player interacting with: " << entity->getName() << std::endl;
        entity->onInteract();
    }
}

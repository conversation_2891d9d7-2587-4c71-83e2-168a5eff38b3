#include "SelectionMenu.h"
#include "TileMap.h"

SelectionMenu::SelectionMenu() : isVisible(false), selectedOption(-1) {
}

void SelectionMenu::show(int mouseX, int mouseY, const Position& clickPos, const std::string& entityName) {
    clickPosition = clickPos;
    isVisible = true;
    selectedOption = -1;
    
    // Clear previous options
    options.clear();
    
    // Add menu options
    options.emplace_back("Interact with " + entityName, MenuAction::INTERACT);
    options.emplace_back("Move to position", MenuAction::MOVE_TO_POSITION);
    
    calculateMenuRect(mouseX, mouseY);
}

void SelectionMenu::hide() {
    isVisible = false;
    options.clear();
    selectedOption = -1;
}

void SelectionMenu::calculateMenuRect(int mouseX, int mouseY) {
    const int optionHeight = 30;
    const int menuWidth = 200;
    const int padding = 10;
    
    int menuHeight = options.size() * optionHeight + padding * 2;
    
    // <PERSON><PERSON><PERSON> bảo menu không bị ra ngoài màn hình
    int menuX = mouseX;
    int menuY = mouseY;
    
    if (menuX + menuWidth > SCREEN_WIDTH) {
        menuX = SCREEN_WIDTH - menuWidth;
    }
    if (menuY + menuHeight > SCREEN_HEIGHT) {
        menuY = SCREEN_HEIGHT - menuHeight;
    }
    
    menuRect = {
        static_cast<float>(menuX),
        static_cast<float>(menuY),
        static_cast<float>(menuWidth),
        static_cast<float>(menuHeight)
    };
    
    // Calculate option rectangles
    for (size_t i = 0; i < options.size(); i++) {
        options[i].rect = {
            static_cast<float>(menuX + padding),
            static_cast<float>(menuY + padding + i * optionHeight),
            static_cast<float>(menuWidth - padding * 2),
            static_cast<float>(optionHeight - 2)
        };
    }
}

MenuAction SelectionMenu::handleClick(int mouseX, int mouseY) {
    if (!isVisible) return MenuAction::NONE;
    
    // Check if click is inside menu
    if (mouseX >= menuRect.x && mouseX <= menuRect.x + menuRect.w &&
        mouseY >= menuRect.y && mouseY <= menuRect.y + menuRect.h) {
        
        // Check which option was clicked
        for (size_t i = 0; i < options.size(); i++) {
            const auto& rect = options[i].rect;
            if (mouseX >= rect.x && mouseX <= rect.x + rect.w &&
                mouseY >= rect.y && mouseY <= rect.y + rect.h) {
                
                MenuAction action = options[i].action;
                hide(); // Hide menu after selection
                return action;
            }
        }
    } else {
        // Click outside menu - hide it
        hide();
    }
    
    return MenuAction::NONE;
}

void SelectionMenu::handleMouseMove(int mouseX, int mouseY) {
    if (!isVisible) return;
    
    selectedOption = -1;
    
    // Check which option is being hovered
    for (size_t i = 0; i < options.size(); i++) {
        const auto& rect = options[i].rect;
        if (mouseX >= rect.x && mouseX <= rect.x + rect.w &&
            mouseY >= rect.y && mouseY <= rect.y + rect.h) {
            selectedOption = static_cast<int>(i);
            break;
        }
    }
}

void SelectionMenu::render(SDL_Renderer* renderer) {
    if (!isVisible) return;
    
    // Draw menu background
    SDL_SetRenderDrawColor(renderer, 50, 50, 50, 200);
    SDL_RenderFillRect(renderer, &menuRect);
    
    // Draw menu border
    SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
    SDL_RenderRect(renderer, &menuRect);
    
    // Draw options
    for (size_t i = 0; i < options.size(); i++) {
        const auto& option = options[i];
        
        // Highlight selected option
        if (selectedOption == static_cast<int>(i)) {
            SDL_SetRenderDrawColor(renderer, 100, 100, 150, 255);
            SDL_RenderFillRect(renderer, &option.rect);
        }
        
        // Draw option border
        SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255);
        SDL_RenderRect(renderer, &option.rect);
        
        // Note: Text rendering would require a font library like SDL_ttf
        // For now, we'll just show colored rectangles with different colors for different actions
        SDL_FRect textRect = {
            option.rect.x + 5,
            option.rect.y + 5,
            option.rect.w - 10,
            option.rect.h - 10
        };
        
        if (option.action == MenuAction::INTERACT) {
            SDL_SetRenderDrawColor(renderer, 255, 200, 100, 255); // Orange for interact
        } else {
            SDL_SetRenderDrawColor(renderer, 100, 200, 255, 255); // Blue for move
        }
        SDL_RenderFillRect(renderer, &textRect);
    }
}

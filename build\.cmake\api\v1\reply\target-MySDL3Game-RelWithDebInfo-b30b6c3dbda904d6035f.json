{"artifacts": [{"path": "RelWithDebInfo/MySDL3Game.exe"}, {"path": "RelWithDebInfo/MySDL3Game.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 14, "parent": 0}, {"command": 1, "file": 0, "line": 24, "parent": 0}, {"command": 2, "file": 0, "line": 10, "parent": 0}, {"command": 2, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++17 -MD"}], "includes": [{"backtrace": 3, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 4, "path": "D:/<PERSON> All/CPP/f0/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "MySDL3Game::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"fragment": "/subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "\"D:\\Frank Work All\\CPP\\f0\\libs\\SDL3\\lib\\x64\\SDL3.lib\"", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "MySDL3Game", "nameOnDisk": "MySDL3Game.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/TileMap.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Player.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Entity.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/EntityManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/SelectionMenu.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}
cmake_minimum_required(VERSION 3.16)
project(MySDL3Game)

set(CMAKE_CXX_STANDARD 17)

# Đường dẫn tới SDL3
set(SDL3_DIR "${CMAKE_SOURCE_DIR}/libs/SDL3")

# Include header
include_directories(${SDL3_DIR}/include)

# Tạo executable
add_executable(${PROJECT_NAME} src/main.cpp)

# Link SDL3
target_link_libraries(${PROJECT_NAME} PRIVATE ${SDL3_DIR}/lib/x64/SDL3.lib)

# Copy SDL3.dll vào folder build sau khi build xong
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${SDL3_DIR}/lib/x64/SDL3.dll"
    $<TARGET_FILE_DIR:${PROJECT_NAME}>
)

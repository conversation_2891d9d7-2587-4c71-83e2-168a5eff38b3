#include <SDL3/SDL.h>
#include <iostream>
#include "TileMap.h"
#include "Player.h"
#include "Position.h"
#include "EntityManager.h"
#include "SelectionMenu.h"

int main(int argc, char *argv[])
{
    if (!SDL_Init(SDL_INIT_VIDEO))
    {
        std::cerr << "SDL Init Error: " << SDL_GetError() << std::endl;
        return 1;
    }

    SDL_Window *window = SDL_CreateWindow("Tilemap Click Game",
                                          SCREEN_WIDTH, SCREEN_HEIGHT,
                                          SDL_WINDOW_OPENGL);

    if (!window)
    {
        std::cerr << "SDL CreateWindow Error: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }

    SDL_Renderer *renderer = SDL_CreateRenderer(window, NULL);
    if (!renderer)
    {
        std::cerr << "SDL CreateRenderer Error: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(window);
        SDL_Quit();
        return 1;
    }

    // Khởi tạo game objects
    TileMap tileMap;
    Player player(5, 5); // Bắt đầu ở vị trí (5, 5)
    EntityManager entityManager;
    SelectionMenu selectionMenu;

    // Tạo entities mặc định
    entityManager.initializeDefaultEntities();

    bool running = true;
    SDL_Event event;

    Uint64 lastTime = SDL_GetTicks();

    while (running)
    {
        Uint64 currentTime = SDL_GetTicks();
        float deltaTime = (currentTime - lastTime) / 1000.0f;
        lastTime = currentTime;

        // Xử lý events
        while (SDL_PollEvent(&event))
        {
            if (event.type == SDL_EVENT_QUIT)
            {
                running = false;
            }
            else if (event.type == SDL_EVENT_MOUSE_BUTTON_DOWN)
            {
                if (event.button.button == SDL_BUTTON_LEFT)
                {
                    // Xử lý click trên selection menu trước
                    MenuAction action = selectionMenu.handleClick(event.button.x, event.button.y);

                    if (action == MenuAction::INTERACT)
                    {
                        // Tương tác với entity tại vị trí click
                        Entity *entity = entityManager.getEntityAt(selectionMenu.getClickPosition());
                        if (entity)
                        {
                            player.interactWith(entity);
                        }
                    }
                    else if (action == MenuAction::MOVE_TO_POSITION)
                    {
                        // Di chuyển đến vị trí
                        player.setTarget(selectionMenu.getClickPosition(), tileMap);
                    }
                    else if (action == MenuAction::NONE && !selectionMenu.getIsVisible())
                    {
                        // Click bình thường trên game world
                        int tileX = event.button.x / TILE_SIZE;
                        int tileY = event.button.y / TILE_SIZE;
                        Position clickPos(tileX, tileY);

                        // Kiểm tra xem có entity tại vị trí click không
                        Entity *entity = entityManager.getEntityAt(clickPos);

                        if (entity)
                        {
                            // Hiển thị menu lựa chọn
                            selectionMenu.show(event.button.x, event.button.y, clickPos, entity->getName());
                        }
                        else
                        {
                            // Di chuyển bình thường
                            player.setTarget(clickPos, tileMap);
                        }
                    }
                }
            }
            else if (event.type == SDL_EVENT_MOUSE_MOTION)
            {
                selectionMenu.handleMouseMove(event.motion.x, event.motion.y);
            }
        }

        // Cập nhật game
        player.update(deltaTime);

        // Render
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255); // Màu đen cho background
        SDL_RenderClear(renderer);

        tileMap.render(renderer);
        entityManager.renderAll(renderer);
        player.render(renderer);
        selectionMenu.render(renderer);

        SDL_RenderPresent(renderer);

        // Giới hạn FPS
        SDL_Delay(16); // ~60 FPS
    }

    SDL_DestroyRenderer(renderer);
    SDL_DestroyWindow(window);
    SDL_Quit();
    return 0;
}

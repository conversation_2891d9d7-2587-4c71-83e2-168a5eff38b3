#include <SDL3/SDL.h>
#include <iostream>
#include "TileMap.h"
#include "Player.h"
#include "Position.h"

int main(int argc, char *argv[])
{
    if (!SDL_Init(SDL_INIT_VIDEO))
    {
        std::cerr << "SDL Init Error: " << SDL_GetError() << std::endl;
        return 1;
    }

    SDL_Window *window = SDL_CreateWindow("Tilemap Click Game",
                                          SCREEN_WIDTH, SCREEN_HEIGHT,
                                          SDL_WINDOW_OPENGL);

    if (!window)
    {
        std::cerr << "SDL CreateWindow Error: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }

    SDL_Renderer *renderer = SDL_CreateRenderer(window, NULL);
    if (!renderer)
    {
        std::cerr << "SDL CreateRenderer Error: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(window);
        SDL_Quit();
        return 1;
    }

    // Khởi tạo game objects
    TileMap tileMap;
    Player player(5, 5); // Bắt đầu ở vị trí (5, 5)

    bool running = true;
    SDL_Event event;
    
    Uint64 lastTime = SDL_GetTicks();

    while (running)
    {
        Uint64 currentTime = SDL_GetTicks();
        float deltaTime = (currentTime - lastTime) / 1000.0f;
        lastTime = currentTime;

        // Xử lý events
        while (SDL_PollEvent(&event))
        {
            if (event.type == SDL_EVENT_QUIT)
            {
                running = false;
            }
            else if (event.type == SDL_EVENT_MOUSE_BUTTON_DOWN)
            {
                if (event.button.button == SDL_BUTTON_LEFT)
                {
                    // Chuyển đổi tọa độ chuột thành tọa độ tile
                    int tileX = event.button.x / TILE_SIZE;
                    int tileY = event.button.y / TILE_SIZE;
                    
                    // Đặt target cho player
                    player.setTarget(Position(tileX, tileY), tileMap);
                }
            }
        }

        // Cập nhật game
        player.update(deltaTime);

        // Render
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255); // Màu đen cho background
        SDL_RenderClear(renderer);

        tileMap.render(renderer);
        player.render(renderer);

        SDL_RenderPresent(renderer);
        
        // Giới hạn FPS
        SDL_Delay(16); // ~60 FPS
    }

    SDL_DestroyRenderer(renderer);
    SDL_DestroyWindow(window);
    SDL_Quit();
    return 0;
}

#include "Entity.h"
#include "TileMap.h"
#include <iostream>

// Entity base class implementation
Entity::Entity(const Position& pos, const std::string& entityName, EntityType entityType)
    : position(pos), name(entityName), type(entityType), isAlive(true) {
    // Default color
    color = {255, 255, 255, 255};
}

// Monster implementation
Monster::Monster(const Position& pos, const std::string& name, int hp, int dmg)
    : Entity(pos, name, EntityType::MONSTER), health(hp), damage(dmg) {
    color = {255, 100, 100, 255}; // Màu đỏ nhạt cho monster
}

void Monster::render(SDL_Renderer* renderer) {
    if (!isAlive) return;
    
    SDL_FRect rect = {
        static_cast<float>(position.x * TILE_SIZE + 4),
        static_cast<float>(position.y * TILE_SIZE + 4),
        static_cast<float>(TILE_SIZE - 8),
        static_cast<float>(TILE_SIZE - 8)
    };
    
    SDL_SetRenderDrawColor(renderer, color.r, color.g, color.b, color.a);
    SDL_RenderFillRect(renderer, &rect);
    
    // Vẽ viền đen
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderRect(renderer, &rect);
    
    // Vẽ "M" để nhận biết là Monster
    SDL_FRect letterRect = {
        static_cast<float>(position.x * TILE_SIZE + TILE_SIZE/2 - 3),
        static_cast<float>(position.y * TILE_SIZE + TILE_SIZE/2 - 3),
        6, 6
    };
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderFillRect(renderer, &letterRect);
}

void Monster::onInteract() {
    std::cout << "Interacting with monster: " << name << std::endl;
    std::cout << "Health: " << health << ", Damage: " << damage << std::endl;
}

std::string Monster::getInteractionText() const {
    return "Fight " + name + " (HP: " + std::to_string(health) + ")";
}

void Monster::takeDamage(int dmg) {
    health -= dmg;
    if (health <= 0) {
        health = 0;
        isAlive = false;
        std::cout << name << " has been defeated!" << std::endl;
    }
}

// NPC implementation
NPC::NPC(const Position& pos, const std::string& name, const std::string& dialog)
    : Entity(pos, name, EntityType::NPC), dialogue(dialog), hasQuest(false) {
    color = {100, 255, 100, 255}; // Màu xanh lá nhạt cho NPC
}

void NPC::render(SDL_Renderer* renderer) {
    SDL_FRect rect = {
        static_cast<float>(position.x * TILE_SIZE + 4),
        static_cast<float>(position.y * TILE_SIZE + 4),
        static_cast<float>(TILE_SIZE - 8),
        static_cast<float>(TILE_SIZE - 8)
    };
    
    SDL_SetRenderDrawColor(renderer, color.r, color.g, color.b, color.a);
    SDL_RenderFillRect(renderer, &rect);
    
    // Vẽ viền đen
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderRect(renderer, &rect);
    
    // Vẽ "N" để nhận biết là NPC
    SDL_FRect letterRect = {
        static_cast<float>(position.x * TILE_SIZE + TILE_SIZE/2 - 3),
        static_cast<float>(position.y * TILE_SIZE + TILE_SIZE/2 - 3),
        6, 6
    };
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderFillRect(renderer, &letterRect);
    
    // Vẽ dấu "!" nếu có quest
    if (hasQuest) {
        SDL_FRect questRect = {
            static_cast<float>(position.x * TILE_SIZE + TILE_SIZE - 8),
            static_cast<float>(position.y * TILE_SIZE + 2),
            4, 8
        };
        SDL_SetRenderDrawColor(renderer, 255, 255, 0, 255); // Màu vàng
        SDL_RenderFillRect(renderer, &questRect);
    }
}

void NPC::onInteract() {
    std::cout << "Talking to NPC: " << name << std::endl;
    std::cout << "\"" << dialogue << "\"" << std::endl;
    if (hasQuest) {
        std::cout << name << " has a quest for you!" << std::endl;
    }
}

std::string NPC::getInteractionText() const {
    std::string text = "Talk to " + name;
    if (hasQuest) {
        text += " (!)";
    }
    return text;
}

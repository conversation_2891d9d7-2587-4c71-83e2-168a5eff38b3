#pragma once

#include <SDL3/SDL.h>
#include <string>
#include "Position.h"

enum class EntityType {
    MONSTER,
    NPC
};

// Base class cho tất cả entities
class Entity {
protected:
    Position position;
    std::string name;
    EntityType type;
    SDL_Color color;
    bool isAlive;
    
public:
    Entity(const Position& pos, const std::string& entityName, EntityType entityType);
    virtual ~Entity() = default;
    
    // Getters
    const Position& getPosition() const { return position; }
    const std::string& getName() const { return name; }
    EntityType getType() const { return type; }
    bool getIsAlive() const { return isAlive; }
    
    // Setters
    void setPosition(const Position& pos) { position = pos; }
    void setAlive(bool alive) { isAlive = alive; }
    
    // Virtual methods
    virtual void render(SDL_Renderer* renderer) = 0;
    virtual void onInteract() = 0; // <PERSON><PERSON><PERSON><PERSON> g<PERSON>i khi player tương tác
    virtual std::string getInteractionText() const = 0;
};

// Monster class
class Monster : public Entity {
private:
    int health;
    int damage;
    
public:
    Monster(const Position& pos, const std::string& name, int hp = 100, int dmg = 20);
    
    void render(SDL_Renderer* renderer) override;
    void onInteract() override;
    std::string getInteractionText() const override;
    
    // Monster specific methods
    int getHealth() const { return health; }
    int getDamage() const { return damage; }
    void takeDamage(int dmg);
};

// NPC class
class NPC : public Entity {
private:
    std::string dialogue;
    bool hasQuest;
    
public:
    NPC(const Position& pos, const std::string& name, const std::string& dialog = "Hello!");
    
    void render(SDL_Renderer* renderer) override;
    void onInteract() override;
    std::string getInteractionText() const override;
    
    // NPC specific methods
    const std::string& getDialogue() const { return dialogue; }
    void setDialogue(const std::string& dialog) { dialogue = dialog; }
    bool getHasQuest() const { return hasQuest; }
    void setHasQuest(bool quest) { hasQuest = quest; }
};

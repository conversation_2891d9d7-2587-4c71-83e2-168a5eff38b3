# 🎮 Tilemap Click Game

Một game đơn giản được viết bằng C++ và SDL3, cho phép người chơi click chuột để di chuyển nhân vật trên tilemap.

## 📁 Cấu trúc dự án

```
f0/
├── include/           # Header files
│   ├── Position.h     # Struct Position
│   ├── TileMap.h      # Class TileMap
│   ├── Player.h       # Class Player
│   ├── Entity.h       # Base Entity, Monster, NPC classes
│   ├── EntityManager.h # Entity management
│   └── SelectionMenu.h # UI selection menu
├── src/               # Source files
│   ├── main.cpp       # Game loop chính
│   ├── TileMap.cpp    # Implementation của TileMap
│   ├── Player.cpp     # Implementation của Player
│   ├── Entity.cpp     # Implementation của Entity system
│   ├── EntityManager.cpp # Implementation của EntityManager
│   └── SelectionMenu.cpp # Implementation của SelectionMenu
├── libs/              # Thư viện SDL3
├── build/             # Build files
└── CMakeLists.txt     # CMake configuration
```

## 🏗️ Kiến trúc code

### **Position.h**
- Struct đơn giản chứa tọa độ x, y
- Operator `==` để so sánh vị trí

### **TileMap.h/.cpp**
- Quản lý bản đồ game với các tile
- Render tilemap với pattern checkerboard
- Kiểm tra tile có thể đi được hay không
- Constants: `TILE_SIZE`, `SCREEN_WIDTH`, `SCREEN_HEIGHT`, `MAP_WIDTH`, `MAP_HEIGHT`

### **Player.h/.cpp**
- Quản lý nhân vật game
- Pathfinding sử dụng thuật toán BFS
- Di chuyển mượt mà giữa các tile
- Render nhân vật, đường đi và target

### **main.cpp**
- Game loop chính
- Xử lý input (mouse click)
- Render tất cả objects
- Quản lý SDL window và renderer

## 🎯 Tính năng

- ✅ **Click-to-move**: Click chuột để di chuyển
- ✅ **Pathfinding**: Tự động tìm đường tối ưu
- ✅ **Smooth movement**: Di chuyển mượt mà
- ✅ **Visual feedback**: Hiển thị đường đi và target
- ✅ **Collision detection**: Không thể đi qua tường
- ✅ **Monsters & NPCs**: Quái vật và NPC trên map
- ✅ **Entity interaction**: Tương tác với entities
- ✅ **Selection menu**: Menu lựa chọn khi click vào entity
- ✅ **Walkthrough entities**: Có thể đi xuyên qua entities

## 🚀 Cách build và chạy

```bash
# Build project
cmake --build build --config Debug

# Chạy game
./build/Debug/MySDL3Game.exe
```

## 🎮 Cách chơi

### **Di chuyển cơ bản:**
1. **Click chuột trái** vào bất kỳ ô trống nào trên map
2. Nhân vật (hình vuông đỏ) sẽ tự động di chuyển đến đó
3. Đường đi được hiển thị bằng các ô màu xanh lá
4. Target được hiển thị bằng ô màu xanh dương

### **Tương tác với Entities:**
1. **Click vào Monster/NPC** để hiển thị menu lựa chọn
2. **Chọn "Interact"** để tương tác với entity
3. **Chọn "Move to position"** để di chuyển đến vị trí đó
4. **Click bên ngoài menu** để đóng menu
5. **Nhân vật có thể đi xuyên qua** monsters và NPCs

## 🎨 Màu sắc

- 🔴 **Đỏ**: Nhân vật
- 🔵 **Xanh dương**: Điểm đích
- 🟢 **Xanh lá**: Đường đi
- ⬜ **Trắng/Xám nhạt**: Đất (có thể đi)
- ⬛ **Xám đậm**: Tường (không thể đi)
- 🟥 **Đỏ nhạt + "M"**: Monsters
- 🟩 **Xanh lá nhạt + "N"**: NPCs
- 🟨 **Dấu "!" vàng**: NPC có quest

## 🔧 Cải tiến code

### **Trước khi refactor:**
- Tất cả code trong 1 file `main.cpp` (~300 dòng)
- Khó maintain và mở rộng
- Không có separation of concerns

### **Sau khi refactor:**
- Code được tách thành nhiều file theo chức năng
- Header files (.h) và implementation files (.cpp) riêng biệt
- Dễ maintain, debug và mở rộng
- Clean architecture với clear responsibilities

## 📝 Ghi chú kỹ thuật

- **Pathfinding**: Sử dụng BFS (Breadth-First Search) để tìm đường đi ngắn nhất
- **Movement**: Interpolation giữa các tile để tạo chuyển động mượt
- **Rendering**: SDL3 với hardware acceleration
- **FPS**: Giới hạn ~60 FPS với `SDL_Delay(16)`

#pragma once

#include <SDL3/SDL.h>
#include <vector>
#include "Position.h"

class TileMap; // Forward declaration
class Entity;  // Forward declaration

// Class nhân vật
class Player
{
private:
    Position currentPos;
    Position targetPos;
    std::vector<Position> path;
    int pathIndex;
    float moveSpeed;
    float pixelX, pixelY; // Vị trí pixel thực tế
    bool hasTarget;

    std::vector<Position> findPath(const Position &start, const Position &end, const TileMap &map);

public:
    Player(int startX, int startY);
    void setTarget(const Position &target, const TileMap &map);
    void update(float deltaTime);
    void render(SDL_Renderer *renderer);

    // Interaction methods
    void interactWith(Entity *entity);
    const Position &getCurrentPosition() const { return currentPos; }
};

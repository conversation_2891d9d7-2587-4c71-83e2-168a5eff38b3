#include "EntityManager.h"
#include <algorithm>

EntityManager::EntityManager() {
    entities.clear();
}

void EntityManager::addMonster(const Position& pos, const std::string& name, int health, int damage) {
    entities.push_back(std::make_unique<Monster>(pos, name, health, damage));
}

void EntityManager::addNPC(const Position& pos, const std::string& name, const std::string& dialogue) {
    entities.push_back(std::make_unique<NPC>(pos, name, dialogue));
}

void EntityManager::removeEntity(size_t index) {
    if (index < entities.size()) {
        entities.erase(entities.begin() + index);
    }
}

void EntityManager::clearEntities() {
    entities.clear();
}

Entity* EntityManager::getEntityAt(const Position& pos) const {
    for (const auto& entity : entities) {
        if (entity->getPosition() == pos) {
            return entity.get();
        }
    }
    return nullptr;
}

void EntityManager::renderAll(SDL_Renderer* renderer) {
    for (const auto& entity : entities) {
        entity->render(renderer);
    }
}

bool EntityManager::hasEntityAt(const Position& pos) const {
    return getEntityAt(pos) != nullptr;
}

void EntityManager::initializeDefaultEntities() {
    // Thêm một số monsters
    addMonster(Position(8, 8), "Goblin", 80, 15);
    addMonster(Position(12, 6), "Orc", 120, 25);
    addMonster(Position(15, 12), "Dragon", 200, 50);
    addMonster(Position(3, 10), "Skeleton", 60, 12);
    
    // Thêm một số NPCs
    addNPC(Position(6, 3), "Village Elder", "Welcome to our village, brave adventurer!");
    addNPC(Position(18, 8), "Merchant", "I have the finest goods in the land!");
    addNPC(Position(10, 14), "Guard", "Stay safe out there!");
    
    // NPC có quest
    auto questNPC = std::make_unique<NPC>(Position(14, 4), "Quest Giver", "I need your help with a dangerous mission!");
    static_cast<NPC*>(questNPC.get())->setHasQuest(true);
    entities.push_back(std::move(questNPC));
}

#pragma once

#include <vector>
#include <memory>
#include <SDL3/SDL.h>
#include "Entity.h"
#include "Position.h"

class EntityManager {
private:
    std::vector<std::unique_ptr<Entity>> entities;
    
public:
    EntityManager();
    ~EntityManager() = default;
    
    // Entity management
    void addMonster(const Position& pos, const std::string& name, int health = 100, int damage = 20);
    void addNPC(const Position& pos, const std::string& name, const std::string& dialogue = "Hello!");
    void removeEntity(size_t index);
    void clearEntities();
    
    // Getters
    const std::vector<std::unique_ptr<Entity>>& getEntities() const { return entities; }
    Entity* getEntityAt(const Position& pos) const;
    size_t getEntityCount() const { return entities.size(); }
    
    // Rendering
    void renderAll(SDL_Renderer* renderer);
    
    // Utility
    bool hasEntityAt(const Position& pos) const;
    void initializeDefaultEntities(); // Tạo một số entities mặc định
};
